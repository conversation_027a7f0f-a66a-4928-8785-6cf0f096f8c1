{"version": 2, "name": "designers-docs", "builds": [{"src": "apps/docs/package.json", "use": "@vercel/next"}], "routes": [{"src": "/(.*)", "dest": "apps/docs/$1"}], "functions": {"apps/docs/app/**/*.tsx": {"runtime": "nodejs18.x"}}, "env": {"NODE_ENV": "production"}, "build": {"env": {"NEXT_TELEMETRY_DISABLED": "1"}}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}]}]}