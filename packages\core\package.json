{"name": "designers-core", "version": "3.0.0", "description": "Core design tokens and utilities for Designers design system", "main": "dist/index.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.esm.js", "require": "./dist/index.js"}, "./tokens": {"types": "./dist/tokens/index.d.ts", "import": "./dist/tokens/index.esm.js", "require": "./dist/tokens/index.js"}}, "files": ["dist", "tokens"], "scripts": {"build": "tsup src/index.ts --format cjs,esm --dts", "dev": "tsup src/index.ts --format cjs,esm --dts --watch", "lint": "eslint src --ext .ts,.tsx", "type-check": "tsc --noEmit", "clean": "rm -rf dist"}, "keywords": ["design-tokens", "design-system", "css-variables", "theming"], "author": "arkit karmokar", "license": "MIT", "publishConfig": {"access": "public"}, "devDependencies": {"@types/node": "^20.10.5", "eslint": "^8.56.0", "tsup": "^8.0.1", "typescript": "^5.3.3"}, "peerDependencies": {}, "dependencies": {}}