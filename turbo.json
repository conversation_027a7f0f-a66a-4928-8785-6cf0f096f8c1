{"$schema": "https://turbo.build/schema.json", "globalDependencies": ["**/.env.*local"], "pipeline": {"build": {"dependsOn": ["^build"], "outputs": ["dist/**", ".next/**", "!.next/cache/**"]}, "dev": {"cache": false, "persistent": true}, "lint": {"dependsOn": ["^build"]}, "test": {"dependsOn": ["^build"], "outputs": ["coverage/**"]}, "type-check": {"dependsOn": ["^build"]}, "clean": {"cache": false}}}