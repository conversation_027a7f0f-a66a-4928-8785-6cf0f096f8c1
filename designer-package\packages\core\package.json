{"name": "@designers/core", "version": "0.1.0", "description": "Core design tokens and utilities for Designers design system", "main": "dist/index.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.esm.js", "require": "./dist/index.js", "types": "./dist/index.d.ts"}, "./tokens": {"import": "./dist/tokens/index.esm.js", "require": "./dist/tokens/index.js", "types": "./dist/tokens/index.d.ts"}}, "files": ["dist", "tokens"], "scripts": {"build": "tsup src/index.ts --format cjs,esm --dts", "dev": "tsup src/index.ts --format cjs,esm --dts --watch", "lint": "eslint src --ext .ts,.tsx", "type-check": "tsc --noEmit", "clean": "rm -rf dist"}, "keywords": ["design-tokens", "design-system", "css-variables", "theming"], "author": "Your Name", "license": "MIT", "devDependencies": {"@types/node": "^20.10.5", "eslint": "^8.56.0", "tsup": "^8.0.1", "typescript": "^5.3.3"}, "peerDependencies": {}, "dependencies": {}}