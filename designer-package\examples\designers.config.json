{"$schema": "https://designers.dev/schema.json", "version": "0.1.0", "name": "My Design System", "description": "Custom design system configuration", "theme": {"default": "light", "autoDetect": true, "storage": true, "themes": {"light": {"colors": {"primary": {"50": "#eff6ff", "100": "#dbeafe", "200": "#bfdbfe", "300": "#93c5fd", "400": "#60a5fa", "500": "#3b82f6", "600": "#2563eb", "700": "#1d4ed8", "800": "#1e40af", "900": "#1e3a8a", "950": "#172554"}, "secondary": {"50": "#f8fafc", "100": "#f1f5f9", "200": "#e2e8f0", "300": "#cbd5e1", "400": "#94a3b8", "500": "#64748b", "600": "#475569", "700": "#334155", "800": "#1e293b", "900": "#0f172a", "950": "#020617"}, "success": {"50": "#f0fdf4", "500": "#22c55e", "900": "#14532d"}, "warning": {"50": "#fffbeb", "500": "#f59e0b", "900": "#78350f"}, "error": {"50": "#fef2f2", "500": "#ef4444", "900": "#7f1d1d"}, "gray": {"50": "#f9fafb", "100": "#f3f4f6", "200": "#e5e7eb", "300": "#d1d5db", "400": "#9ca3af", "500": "#6b7280", "600": "#4b5563", "700": "#374151", "800": "#1f2937", "900": "#111827", "950": "#030712"}}, "semantic": {"text": {"primary": "#111827", "secondary": "#374151", "tertiary": "#6b7280", "inverse": "#ffffff", "disabled": "#9ca3af"}, "background": {"primary": "#ffffff", "secondary": "#f9fafb", "tertiary": "#f3f4f6", "inverse": "#111827", "overlay": "rgba(0, 0, 0, 0.5)"}, "border": {"primary": "#e5e7eb", "secondary": "#d1d5db", "tertiary": "#9ca3af", "focus": "#3b82f6", "error": "#ef4444", "success": "#22c55e"}, "interactive": {"primary": "#2563eb", "primaryHover": "#1d4ed8", "primaryActive": "#1e40af", "secondary": "#f3f4f6", "secondaryHover": "#e5e7eb", "secondaryActive": "#d1d5db"}}}, "dark": {"colors": {"primary": {"50": "#eff6ff", "500": "#3b82f6", "900": "#1e3a8a"}}, "semantic": {"text": {"primary": "#f3f4f6", "secondary": "#d1d5db", "tertiary": "#6b7280", "inverse": "#111827", "disabled": "#4b5563"}, "background": {"primary": "#111827", "secondary": "#1f2937", "tertiary": "#374151", "inverse": "#ffffff", "overlay": "rgba(0, 0, 0, 0.7)"}, "border": {"primary": "#374151", "secondary": "#4b5563", "tertiary": "#6b7280", "focus": "#60a5fa", "error": "#f87171", "success": "#4ade80"}, "interactive": {"primary": "#3b82f6", "primaryHover": "#60a5fa", "primaryActive": "#93c5fd", "secondary": "#374151", "secondaryHover": "#4b5563", "secondaryActive": "#6b7280"}}}}}, "typography": {"fontFamily": {"sans": ["Inter", "-apple-system", "BlinkMacSystemFont", "Segoe UI", "Roboto", "sans-serif"], "serif": ["Charter", "Bitstream Charter", "Sitka Text", "Cambria", "serif"], "mono": ["JetBrains Mono", "Fira Code", "Consolas", "Liberation Mono", "monospace"]}, "fontWeight": {"thin": 100, "light": 300, "normal": 400, "medium": 500, "semibold": 600, "bold": 700, "extrabold": 800, "black": 900}, "fontSize": {"xs": "0.75rem", "sm": "0.875rem", "base": "1rem", "lg": "1.125rem", "xl": "1.25rem", "2xl": "1.5rem", "3xl": "1.875rem", "4xl": "2.25rem", "5xl": "3rem", "6xl": "3.75rem"}, "lineHeight": {"none": 1, "tight": 1.25, "snug": 1.375, "normal": 1.5, "relaxed": 1.625, "loose": 2}, "letterSpacing": {"tighter": "-0.05em", "tight": "-0.025em", "normal": "0em", "wide": "0.025em", "wider": "0.05em", "widest": "0.1em"}, "scales": {"heading": {"h1": {"fontSize": "clamp(2.25rem, 4vw, 4.5rem)", "lineHeight": "1.1", "letterSpacing": "-0.025em", "fontWeight": 700}, "h2": {"fontSize": "clamp(1.875rem, 3.5vw, 3.75rem)", "lineHeight": "1.2", "letterSpacing": "-0.025em", "fontWeight": 600}, "h3": {"fontSize": "clamp(1.5rem, 3vw, 3rem)", "lineHeight": "1.25", "letterSpacing": "-0.02em", "fontWeight": 600}}, "body": {"large": {"fontSize": "1.125rem", "lineHeight": "1.6"}, "base": {"fontSize": "1rem", "lineHeight": "1.5"}, "small": {"fontSize": "0.875rem", "lineHeight": "1.4"}}}}, "spacing": {"scale": {"0": "0px", "px": "1px", "0.5": "0.125rem", "1": "0.25rem", "1.5": "0.375rem", "2": "0.5rem", "2.5": "0.625rem", "3": "0.75rem", "3.5": "0.875rem", "4": "1rem", "5": "1.25rem", "6": "1.5rem", "7": "1.75rem", "8": "2rem", "9": "2.25rem", "10": "2.5rem", "12": "3rem", "14": "3.5rem", "16": "4rem", "20": "5rem", "24": "6rem", "28": "7rem", "32": "8rem", "36": "9rem", "40": "10rem", "48": "12rem", "56": "14rem", "64": "16rem", "72": "18rem", "80": "20rem", "96": "24rem"}, "semantic": {"component": {"xs": "0.25rem", "sm": "0.5rem", "md": "1rem", "lg": "1.5rem", "xl": "2rem"}, "layout": {"xs": "1rem", "sm": "1.5rem", "md": "2rem", "lg": "3rem", "xl": "4rem", "2xl": "6rem", "3xl": "8rem"}}}, "responsive": {"autoDetect": true, "breakpoints": {"xs": {"min": "0px", "max": "639px", "cols": 4, "gutter": "1rem"}, "sm": {"min": "640px", "max": "767px", "cols": 8, "gutter": "1.5rem"}, "md": {"min": "768px", "max": "1023px", "cols": 8, "gutter": "2rem"}, "lg": {"min": "1024px", "max": "1279px", "cols": 12, "gutter": "2rem"}, "xl": {"min": "1280px", "max": "1535px", "cols": 12, "gutter": "2.5rem"}, "2xl": {"min": "1536px", "cols": 12, "gutter": "3rem"}}, "containerSizes": {"xs": "100%", "sm": "640px", "md": "768px", "lg": "1024px", "xl": "1280px", "2xl": "1536px"}}, "effects": {"shadows": {"none": "none", "xs": "0 1px 2px 0 rgba(0, 0, 0, 0.05)", "sm": "0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1)", "md": "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1)", "lg": "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1)", "xl": "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1)", "2xl": "0 25px 50px -12px rgba(0, 0, 0, 0.25)", "inner": "inset 0 2px 4px 0 rgba(0, 0, 0, 0.05)"}, "borderRadius": {"none": "0", "xs": "0.125rem", "sm": "0.25rem", "md": "0.375rem", "lg": "0.5rem", "xl": "0.75rem", "2xl": "1rem", "3xl": "1.5rem", "full": "9999px"}, "gradients": {"primary": "linear-gradient(135deg, #667eea 0%, #764ba2 100%)", "secondary": "linear-gradient(135deg, #f093fb 0%, #f5576c 100%)", "success": "linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)", "warning": "linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)", "error": "linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)"}, "blur": {"none": "0", "xs": "2px", "sm": "4px", "md": "8px", "lg": "12px", "xl": "16px", "2xl": "24px", "3xl": "40px"}, "opacity": {"0": "0", "5": "0.05", "10": "0.1", "25": "0.25", "50": "0.5", "75": "0.75", "90": "0.9", "95": "0.95", "100": "1"}}, "tokens": {"prefix": "designers", "output": "./src/styles/tokens", "formats": ["css", "ts", "scss"], "include": ["colors", "typography", "spacing", "effects"], "exclude": [], "cssVariables": {"generateFor": ["light", "dark"], "selector": ":root", "mediaQueries": {"dark": "@media (prefers-color-scheme: dark)"}}}, "components": {"output": "./src/components/ui", "typescript": true, "styling": "css-in-js", "library": {"button": {"variants": {"primary": {"backgroundColor": "semantic.interactive.primary", "color": "semantic.text.inverse", "border": "none", "hover": {"backgroundColor": "semantic.interactive.primaryHover"}, "active": {"backgroundColor": "semantic.interactive.primaryActive"}}, "secondary": {"backgroundColor": "semantic.interactive.secondary", "color": "semantic.text.primary", "border": "1px solid semantic.border.primary", "hover": {"backgroundColor": "semantic.interactive.secondaryHover"}}, "outline": {"backgroundColor": "transparent", "color": "semantic.interactive.primary", "border": "1px solid semantic.interactive.primary", "hover": {"backgroundColor": "semantic.interactive.primary", "color": "semantic.text.inverse"}}, "ghost": {"backgroundColor": "transparent", "color": "semantic.text.primary", "border": "none", "hover": {"backgroundColor": "semantic.interactive.secondary"}}, "destructive": {"backgroundColor": "colors.error.500", "color": "semantic.text.inverse", "border": "none", "hover": {"backgroundColor": "colors.error.600"}}}, "sizes": {"xs": {"padding": "spacing.1 spacing.2", "fontSize": "typography.fontSize.xs", "borderRadius": "effects.borderRadius.sm"}, "sm": {"padding": "spacing.1 spacing.3", "fontSize": "typography.fontSize.sm", "borderRadius": "effects.borderRadius.md"}, "md": {"padding": "spacing.2 spacing.4", "fontSize": "typography.fontSize.base", "borderRadius": "effects.borderRadius.md"}, "lg": {"padding": "spacing.3 spacing.6", "fontSize": "typography.fontSize.lg", "borderRadius": "effects.borderRadius.lg"}, "xl": {"padding": "spacing.4 spacing.8", "fontSize": "typography.fontSize.xl", "borderRadius": "effects.borderRadius.lg"}}, "states": {"disabled": {"opacity": "effects.opacity.50", "cursor": "not-allowed"}, "loading": {"opacity": "effects.opacity.75", "cursor": "wait"}}}, "input": {"variants": {"default": {"backgroundColor": "semantic.background.primary", "color": "semantic.text.primary", "border": "1px solid semantic.border.primary", "focus": {"borderColor": "semantic.border.focus", "boxShadow": "0 0 0 3px rgba(59, 130, 246, 0.1)"}}, "error": {"backgroundColor": "semantic.background.primary", "color": "semantic.text.primary", "border": "1px solid semantic.border.error", "focus": {"borderColor": "semantic.border.error", "boxShadow": "0 0 0 3px rgba(239, 68, 68, 0.1)"}}, "success": {"backgroundColor": "semantic.background.primary", "color": "semantic.text.primary", "border": "1px solid semantic.border.success", "focus": {"borderColor": "semantic.border.success", "boxShadow": "0 0 0 3px rgba(34, 197, 94, 0.1)"}}}, "sizes": {"sm": {"padding": "spacing.2 spacing.3", "fontSize": "typography.fontSize.sm", "borderRadius": "effects.borderRadius.md"}, "md": {"padding": "spacing.2.5 spacing.3.5", "fontSize": "typography.fontSize.base", "borderRadius": "effects.borderRadius.md"}, "lg": {"padding": "spacing.3 spacing.4", "fontSize": "typography.fontSize.lg", "borderRadius": "effects.borderRadius.lg"}}}, "card": {"variants": {"default": {"backgroundColor": "semantic.background.primary", "color": "semantic.text.primary", "border": "1px solid semantic.border.primary", "borderRadius": "effects.borderRadius.lg"}, "elevated": {"backgroundColor": "semantic.background.primary", "color": "semantic.text.primary", "border": "none", "borderRadius": "effects.borderRadius.lg", "boxShadow": "effects.shadows.md"}, "outlined": {"backgroundColor": "transparent", "color": "semantic.text.primary", "border": "2px solid semantic.border.primary", "borderRadius": "effects.borderRadius.lg"}}, "sizes": {"sm": {"padding": "spacing.4"}, "md": {"padding": "spacing.6"}, "lg": {"padding": "spacing.8"}}}}}, "animations": {"enabled": true, "respectReducedMotion": true, "library": "framer-motion", "durations": {"instant": "0ms", "fast": "150ms", "normal": "300ms", "slow": "500ms", "slower": "750ms", "slowest": "1000ms"}, "easings": {"linear": "linear", "easeIn": "cubic-bezier(0.4, 0, 1, 1)", "easeOut": "cubic-bezier(0, 0, 0.2, 1)", "easeInOut": "cubic-bezier(0.4, 0, 0.2, 1)", "bounce": "cubic-bezier(0.68, -0.55, 0.265, 1.55)", "elastic": "cubic-bezier(0.175, 0.885, 0.32, 1.275)"}, "presets": {"fadeIn": {"initial": {"opacity": 0}, "animate": {"opacity": 1}, "duration": "normal", "easing": "easeOut"}, "fadeOut": {"initial": {"opacity": 1}, "animate": {"opacity": 0}, "duration": "normal", "easing": "easeIn"}, "slideUp": {"initial": {"y": 20, "opacity": 0}, "animate": {"y": 0, "opacity": 1}, "duration": "normal", "easing": "easeOut"}, "slideDown": {"initial": {"y": -20, "opacity": 0}, "animate": {"y": 0, "opacity": 1}, "duration": "normal", "easing": "easeOut"}, "slideLeft": {"initial": {"x": 20, "opacity": 0}, "animate": {"x": 0, "opacity": 1}, "duration": "normal", "easing": "easeOut"}, "slideRight": {"initial": {"x": -20, "opacity": 0}, "animate": {"x": 0, "opacity": 1}, "duration": "normal", "easing": "easeOut"}, "scale": {"initial": {"scale": 0.95, "opacity": 0}, "animate": {"scale": 1, "opacity": 1}, "duration": "normal", "easing": "easeOut"}, "scaleIn": {"initial": {"scale": 0}, "animate": {"scale": 1}, "duration": "normal", "easing": "bounce"}, "rotate": {"initial": {"rotate": -5, "opacity": 0}, "animate": {"rotate": 0, "opacity": 1}, "duration": "normal", "easing": "easeOut"}, "bounce": {"initial": {"y": -10}, "animate": {"y": 0}, "duration": "normal", "easing": "bounce"}, "pulse": {"animate": {"scale": [1, 1.05, 1]}, "duration": "slow", "repeat": "Infinity", "easing": "easeInOut"}, "shake": {"animate": {"x": [-2, 2, -2, 2, 0]}, "duration": "fast", "easing": "easeInOut"}}, "transitions": {"page": {"initial": {"opacity": 0, "y": 20}, "animate": {"opacity": 1, "y": 0}, "exit": {"opacity": 0, "y": -20}, "duration": "normal"}, "modal": {"initial": {"opacity": 0, "scale": 0.95}, "animate": {"opacity": 1, "scale": 1}, "exit": {"opacity": 0, "scale": 0.95}, "duration": "fast"}, "drawer": {"initial": {"x": "-100%"}, "animate": {"x": 0}, "exit": {"x": "-100%"}, "duration": "normal"}}}, "iconography": {"library": "lucide-react", "sizes": {"xs": "12px", "sm": "16px", "md": "20px", "lg": "24px", "xl": "32px", "2xl": "48px"}, "strokeWidth": {"thin": 1, "normal": 1.5, "medium": 2, "thick": 2.5, "bold": 3}, "sets": {"interface": ["menu", "close", "chevron-down", "chevron-up", "chevron-left", "chevron-right", "search", "filter", "settings"], "actions": ["plus", "minus", "edit", "trash", "save", "download", "upload", "copy", "share"], "status": ["check", "x", "alert-circle", "info", "help-circle", "warning"], "navigation": ["home", "user", "mail", "bell", "heart", "bookmark", "star"], "media": ["play", "pause", "stop", "volume", "image", "video", "music"]}}, "layout": {"containers": {"xs": "320px", "sm": "640px", "md": "768px", "lg": "1024px", "xl": "1280px", "2xl": "1536px", "full": "100%"}, "grid": {"columns": {"xs": 4, "sm": 8, "md": 8, "lg": 12, "xl": 12, "2xl": 12}, "gutters": {"xs": "1rem", "sm": "1.5rem", "md": "2rem", "lg": "2rem", "xl": "2.5rem", "2xl": "3rem"}, "margins": {"xs": "1rem", "sm": "1.5rem", "md": "2rem", "lg": "3rem", "xl": "4rem", "2xl": "6rem"}}, "zIndex": {"hide": -1, "base": 0, "docked": 10, "dropdown": 1000, "sticky": 1100, "banner": 1200, "overlay": 1300, "modal": 1400, "popover": 1500, "skipLink": 1600, "toast": 1700, "tooltip": 1800}, "aspectRatios": {"square": "1/1", "video": "16/9", "photo": "4/3", "golden": "1.618/1", "ultrawide": "21/9", "portrait": "3/4"}}, "states": {"interactive": {"hover": {"scale": 1.02, "brightness": 1.1, "transition": "all 0.2s ease"}, "active": {"scale": 0.98, "brightness": 0.9, "transition": "all 0.1s ease"}, "focus": {"outline": "2px solid", "outlineColor": "semantic.border.focus", "outlineOffset": "2px"}, "disabled": {"opacity": 0.5, "cursor": "not-allowed", "pointerEvents": "none"}}, "loading": {"opacity": 0.7, "cursor": "wait", "animation": "pulse 2s infinite"}, "error": {"borderColor": "semantic.border.error", "backgroundColor": "colors.error.50", "color": "colors.error.700"}, "success": {"borderColor": "semantic.border.success", "backgroundColor": "colors.success.50", "color": "colors.success.700"}, "warning": {"borderColor": "colors.warning.500", "backgroundColor": "colors.warning.50", "color": "colors.warning.700"}}, "integrations": {"uiLibrary": null, "styling": "css", "bundler": "vite", "framework": "react", "storybook": {"enabled": false, "addons": ["@storybook/addon-docs"]}, "tailwind": {"enabled": true, "configPath": "./tailwind.config.js", "autoGenerate": true, "watch": true}}, "ui": {"shadcn": {"enabled": false, "components": [], "theme": "inherit", "customizations": {"borderRadius": "0.5rem", "cssVariables": true}}, "mui": {"enabled": false, "components": [], "theme": "inherit", "customizations": {"palette": {"mode": "light"}}}, "chakra": {"enabled": false, "components": [], "theme": "inherit", "customizations": {"colorMode": "light"}}, "mantine": {"enabled": false, "components": [], "theme": "inherit", "customizations": {"colorScheme": "light"}}}, "content": {"images": {"placeholder": {"background": "semantic.background.tertiary", "color": "semantic.text.tertiary", "text": "Image"}, "aspectRatios": {"square": "1:1", "landscape": "16:9", "portrait": "3:4", "wide": "21:9"}, "sizes": {"thumbnail": "64px", "small": "128px", "medium": "256px", "large": "512px", "hero": "100vw"}}, "avatars": {"sizes": {"xs": "24px", "sm": "32px", "md": "40px", "lg": "48px", "xl": "64px", "2xl": "96px"}, "fallback": {"background": "semantic.background.secondary", "color": "semantic.text.secondary", "fontWeight": "typography.fontWeight.medium"}}, "logos": {"sizes": {"xs": "16px", "sm": "24px", "md": "32px", "lg": "48px", "xl": "64px"}}}, "forms": {"validation": {"required": {"indicator": "*", "color": "colors.error.500"}, "error": {"borderColor": "semantic.border.error", "backgroundColor": "colors.error.50", "textColor": "colors.error.700", "iconColor": "colors.error.500"}, "success": {"borderColor": "semantic.border.success", "backgroundColor": "colors.success.50", "textColor": "colors.success.700", "iconColor": "colors.success.500"}, "warning": {"borderColor": "colors.warning.500", "backgroundColor": "colors.warning.50", "textColor": "colors.warning.700", "iconColor": "colors.warning.500"}}, "fieldSizes": {"sm": {"height": "32px", "padding": "spacing.1 spacing.2", "fontSize": "typography.fontSize.sm"}, "md": {"height": "40px", "padding": "spacing.2 spacing.3", "fontSize": "typography.fontSize.base"}, "lg": {"height": "48px", "padding": "spacing.3 spacing.4", "fontSize": "typography.fontSize.lg"}}, "labels": {"fontSize": "typography.fontSize.sm", "fontWeight": "typography.fontWeight.medium", "color": "semantic.text.primary", "marginBottom": "spacing.1"}, "helpText": {"fontSize": "typography.fontSize.xs", "color": "semantic.text.tertiary", "marginTop": "spacing.1"}}, "feedback": {"alerts": {"variants": {"info": {"backgroundColor": "colors.primary.50", "borderColor": "colors.primary.200", "textColor": "colors.primary.800", "iconColor": "colors.primary.500"}, "success": {"backgroundColor": "colors.success.50", "borderColor": "colors.success.200", "textColor": "colors.success.800", "iconColor": "colors.success.500"}, "warning": {"backgroundColor": "colors.warning.50", "borderColor": "colors.warning.200", "textColor": "colors.warning.800", "iconColor": "colors.warning.500"}, "error": {"backgroundColor": "colors.error.50", "borderColor": "colors.error.200", "textColor": "colors.error.800", "iconColor": "colors.error.500"}}}, "toasts": {"position": "top-right", "duration": 5000, "maxVisible": 3, "spacing": "spacing.2"}, "loading": {"spinner": {"size": "20px", "color": "semantic.interactive.primary", "strokeWidth": "2px"}, "skeleton": {"baseColor": "semantic.background.secondary", "highlightColor": "semantic.background.tertiary", "borderRadius": "effects.borderRadius.md"}, "progress": {"height": "8px", "backgroundColor": "semantic.background.secondary", "fillColor": "semantic.interactive.primary", "borderRadius": "effects.borderRadius.full"}}}, "data": {"tables": {"headerBackground": "semantic.background.secondary", "headerColor": "semantic.text.primary", "headerFontWeight": "typography.fontWeight.semibold", "rowHover": "semantic.background.tertiary", "borderColor": "semantic.border.primary", "cellPadding": "spacing.3 spacing.4"}, "charts": {"colors": ["colors.primary.500", "colors.secondary.500", "colors.success.500", "colors.warning.500", "colors.error.500", "colors.primary.300", "colors.secondary.300", "colors.success.300"], "grid": {"color": "semantic.border.secondary", "strokeWidth": "1px"}, "axis": {"color": "semantic.text.tertiary", "fontSize": "typography.fontSize.xs"}}}, "build": {"watch": ["src/**/*.{ts,tsx,js,jsx}"], "output": {"tokens": "./dist/tokens", "components": "./dist/components"}, "optimization": {"treeshaking": true, "minification": true, "sourcemaps": true}}, "development": {"hotReload": true, "devtools": true, "playground": {"enabled": true, "port": 3001}}, "accessibility": {"enforceContrast": true, "minimumContrastRatio": 4.5, "focusVisible": true, "reducedMotion": "respect"}, "performance": {"lazyLoading": true, "bundleSize": {"maxSize": "50kb", "warn": "30kb"}, "caching": {"tokens": true, "components": false}}}