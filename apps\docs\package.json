{"name": "designers-docs", "version": "1.0.0", "private": true, "description": "Documentation site for Designers - Advanced theme mode design system", "homepage": "https://designers-docs.vercel.app", "repository": {"type": "git", "url": "https://github.com/Arkit-k/Designers.git", "directory": "apps/docs"}, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "export": "next build && next export", "deploy": "vercel --prod"}, "dependencies": {"@tailwindcss/typography": "^0.5.16", "autoprefixer": "^10.4.16", "clsx": "^2.0.0", "designers": "^3.1.0", "framer-motion": "^10.16.16", "lucide-react": "^0.294.0", "next": "14.0.4", "next-themes": "^0.2.1", "postcss": "^8.4.32", "prismjs": "^1.29.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-syntax-highlighter": "^15.5.0", "tailwind-merge": "^2.2.0", "tailwindcss": "^3.4.0"}, "devDependencies": {"@types/node": "^20.10.5", "@types/prismjs": "^1.26.3", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@types/react-syntax-highlighter": "^15.5.11", "eslint": "^8.56.0", "eslint-config-next": "14.0.4", "typescript": "^5.3.3"}}